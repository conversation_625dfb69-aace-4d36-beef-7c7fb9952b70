<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "老板抽卡"
  }
}
</route>

<script setup lang="ts">
defineOptions({
  name: 'DrawCard',
})

// 卡片类型定义
interface Card {
  id: number
  name: string
  rarity: 'legendary' | 'epic' | 'rare' | 'common'
  image: string
  description: string
}

// 卡片数据
const cards = ref<Card[]>([
  { id: 1, name: '传说级老板', rarity: 'legendary', image: '🎯', description: '超级稀有的老板卡' },
  { id: 2, name: '史诗级老板', rarity: 'epic', image: '⭐', description: '非常稀有的老板卡' },
  { id: 3, name: '稀有级老板', rarity: 'rare', image: '💎', description: '稀有的老板卡' },
  { id: 4, name: '普通级老板', rarity: 'common', image: '🔸', description: '常见的老板卡' },
])

// 抽卡结果
const drawResult = ref<Card | null>(null)
const isDrawing = ref(false)

// 抽卡概率
const rarityProbability = {
  legendary: 0.01, // 1%
  epic: 0.05, // 5%
  rare: 0.24, // 24%
  common: 0.70, // 70%
}

// 抽卡函数
function drawCard() {
  if (isDrawing.value)
    return

  isDrawing.value = true
  drawResult.value = null

  // 模拟抽卡动画延迟
  setTimeout(() => {
    const random = Math.random()
    let selectedCard: Card | undefined

    if (random < rarityProbability.legendary) {
      selectedCard = cards.value.find(card => card.rarity === 'legendary')
    }
    else if (random < rarityProbability.legendary + rarityProbability.epic) {
      selectedCard = cards.value.find(card => card.rarity === 'epic')
    }
    else if (random < rarityProbability.legendary + rarityProbability.epic + rarityProbability.rare) {
      selectedCard = cards.value.find(card => card.rarity === 'rare')
    }
    else {
      selectedCard = cards.value.find(card => card.rarity === 'common')
    }

    drawResult.value = selectedCard || null
    isDrawing.value = false
  }, 2000)
}

// 获取稀有度颜色
function getRarityColor(rarity: Card['rarity']) {
  const colors: Record<Card['rarity'], string> = {
    legendary: '#ff6b35',
    epic: '#9b59b6',
    rare: '#3498db',
    common: '#95a5a6',
  }
  return colors[rarity] || colors.common
}

// 重置抽卡
function resetDraw() {
  drawResult.value = null
}
</script>

<template>
  <view class="min-h-screen from-blue-100 to-purple-100 bg-gradient-to-b p-4">
    <!-- 标题 -->
    <view class="py-6 text-center">
      <text class="text-3xl text-purple-600 font-bold">
        老板抽卡
      </text>
      <text class="mt-2 text-sm text-gray-600">
        试试你的运气，抽取稀有老板卡！
      </text>
    </view>

    <!-- 抽卡区域 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-lg">
      <!-- 抽卡按钮 -->
      <view class="mb-6 text-center">
        <button
          class="rounded-full from-purple-500 to-pink-500 bg-gradient-to-r px-8 py-4 text-lg text-white font-bold shadow-lg"
          :class="{ 'opacity-50': isDrawing }"
          :disabled="isDrawing"
          @click="drawCard"
        >
          {{ isDrawing ? '抽卡中...' : '开始抽卡' }}
        </button>
      </view>

      <!-- 抽卡动画/结果 -->
      <view class="text-center">
        <view v-if="isDrawing" class="mb-4 animate-spin text-6xl">
          🎰
        </view>

        <view v-if="drawResult && !isDrawing" class="animate-bounce">
          <view class="mb-4 rounded-2xl from-yellow-200 to-yellow-300 bg-gradient-to-r p-6">
            <text class="mb-4 text-6xl">
              {{ drawResult.image }}
            </text>
            <text class="mb-2 text-2xl font-bold" :style="{ color: getRarityColor(drawResult.rarity) }">
              {{ drawResult.name }}
            </text>
            <text class="text-gray-600">
              {{ drawResult.description }}
            </text>
          </view>

          <button
            class="rounded-full bg-gray-500 px-6 py-2 text-white"
            @click="resetDraw"
          >
            再抽一次
          </button>
        </view>
      </view>
    </view>

    <!-- 卡片展示 -->
    <view class="rounded-2xl bg-white p-6 shadow-lg">
      <text class="mb-4 text-xl text-gray-800 font-bold">
        卡片图鉴
      </text>
      <view class="grid grid-cols-2 gap-4">
        <view
          v-for="card in cards"
          :key="card.id"
          class="rounded-xl bg-gray-50 p-4 text-center"
          :style="{ borderLeft: `4px solid ${getRarityColor(card.rarity)}` }"
        >
          <text class="mb-2 text-3xl">
            {{ card.image }}
          </text>
          <text class="text-sm font-bold" :style="{ color: getRarityColor(card.rarity) }">
            {{ card.name }}
          </text>
          <text class="mt-1 text-xs text-gray-500">
            {{ card.description }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
