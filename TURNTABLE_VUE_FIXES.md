# Turntable组件Vue化修复

## 问题描述

原始错误：
```
vue.runtime.esm.js:1443 TypeError: Cannot set properties of undefined (setting 'cursor')
    at updateButtonText (turntable.vue:114:1)
    at handleSpin (turntable.vue:173:1)
```

**问题原因**: 在Vue组件中直接操作DOM元素的style属性会出现问题，特别是在组件初始化阶段，ref可能还没有绑定到实际的DOM元素。

## 解决方案

### 1. 替换DOM操作为响应式数据

**之前的做法** (有问题):
```javascript
// 直接操作DOM
function updateButtonText() {
  if (!btnRef.value) return
  
  if (spinCount.value > 0) {
    btnRef.value.textContent = `抽奖 (${spinCount.value})`
    btnRef.value.style.cursor = 'pointer'
  } else {
    btnRef.value.textContent = '无抽奖次数'
    btnRef.value.style.cursor = 'not-allowed'
  }
}

// 直接操作DOM进行动画
if (boxRef.value) {
  boxRef.value.style.transform = `rotate(${current}deg)`
}
if (btnRef.value) {
  btnRef.value.style.transform = `translate(-50%, -50%) rotate(${-current}deg)`
}
```

**修复后的做法** (Vue响应式):
```javascript
// 新增响应式数据
const buttonText = ref('抽奖')
const buttonCursor = ref('pointer')
const buttonDisabled = ref(false)
const boxRotation = ref(0)
const btnRotation = ref(0)

// 使用响应式数据更新状态
function updateButtonText() {
  if (spinCount.value > 0) {
    buttonText.value = `抽奖 (${spinCount.value})`
    buttonCursor.value = 'pointer'
    buttonDisabled.value = false
  } else {
    buttonText.value = '无抽奖次数'
    buttonCursor.value = 'not-allowed'
    buttonDisabled.value = true
  }
}

// 使用响应式数据更新旋转角度
boxRotation.value = current
btnRotation.value = -current
```

### 2. 更新模板绑定

**模板中使用响应式数据**:
```vue
<template>
  <view 
    id="box" 
    ref="boxRef" 
    class="turntable-box"
    :style="{ transform: `rotate(${boxRotation}deg)` }"
  >
    <!-- ... 奖品区域 ... -->
    
    <!-- 抽奖按钮 -->
    <view
      id="btn"
      ref="btnRef"
      class="spin-btn"
      :class="{ disabled: buttonDisabled }"
      :style="{ 
        transform: `translate(-50%, -50%) rotate(${btnRotation}deg)`,
        cursor: buttonCursor
      }"
      @click="handleSpin"
    >
      {{ buttonText }}
    </view>
  </view>
</template>
```

### 3. 添加禁用状态样式

```scss
.spin-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}

.spin-btn.disabled::after {
  opacity: 0.6;
}
```

### 4. 改进点击处理逻辑

```javascript
async function handleSpin() {
  // 增加buttonDisabled检查
  if (clickLock.value || spinCount.value <= 0 || buttonDisabled.value) {
    return
  }
  clickLock.value = true
  buttonDisabled.value = true // 立即禁用按钮
  
  // ... 抽奖逻辑 ...
}

// 在动画完成后重新启用按钮
timer = setInterval(() => {
  if (current >= finalAngle) {
    clickLock.value = false
    if (timer) clearInterval(timer)
    updateButtonText() // 重新更新按钮状态
    emit('spinResult', prize)
  }
  // ...
}, 16)
```

## 修复的核心原则

### 1. **避免直接DOM操作**
- 在Vue组件中，应该使用响应式数据驱动视图更新
- 避免直接操作`ref.value.style`或`ref.value.textContent`

### 2. **使用声明式渲染**
- 通过`:style`、`:class`、`{{ }}`等Vue指令绑定数据
- 让Vue的响应式系统自动处理DOM更新

### 3. **状态管理集中化**
- 将所有UI状态（按钮文本、旋转角度、禁用状态等）用响应式数据管理
- 通过函数更新状态，而不是直接操作DOM

### 4. **生命周期安全**
- 响应式数据在组件的整个生命周期中都是安全的
- 避免在DOM元素可能不存在时进行操作

## 测试验证

✅ 修复后组件可以正常初始化  
✅ 按钮状态正确显示和更新  
✅ 转盘旋转动画正常工作  
✅ 禁用状态正确处理  
✅ 热更新正常工作  
✅ 无控制台错误  

## 总结

通过将所有DOM操作替换为Vue的响应式数据绑定，成功解决了`Cannot set properties of undefined`错误。这种方法不仅修复了错误，还让代码更符合Vue的最佳实践，提高了代码的可维护性和可靠性。

**关键改进**:
- ✅ 完全移除直接DOM操作
- ✅ 使用响应式数据驱动UI更新  
- ✅ 改善用户交互体验
- ✅ 提高代码健壮性
- ✅ 符合Vue最佳实践
